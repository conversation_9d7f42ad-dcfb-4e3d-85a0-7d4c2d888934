/**
 * Type-safe environment variable accessor
 * Provides autocomplete and type checking for environment variable keys
 */

/**
 * Get an environment variable with type safety and optional default value
 * @param key - The environment variable key (typed)
 * @param defaultValue - Default value if the environment variable is not set
 * @returns The environment variable value or default value
 */
export const env = <K extends keyof EnvironmentVariables>(
  key: K,
  defaultValue: string = ''
): string => {
  return process.env[key] ?? defaultValue
}

/**
 * Get a required environment variable (throws if not found)
 * @param key - The environment variable key (typed)
 * @returns The environment variable value
 * @throws Error if the environment variable is not set
 */
export const requiredEnv = <K extends keyof EnvironmentVariables>(
  key: K
): string => {
  const value = process.env[key]
  if (!value) {
    throw new Error(`Required environment variable ${String(key)} is not set`)
  }
  return value
}

/**
 * Get an environment variable as a number
 * @param key - The environment variable key (typed)
 * @param defaultValue - Default value if the environment variable is not set
 * @returns The environment variable value as a number
 */
export const envNumber = <K extends keyof EnvironmentVariables>(
  key: K,
  defaultValue: number = 0
): number => {
  const value = process.env[key]
  if (!value) return defaultValue

  const parsed = parseInt(value, 10)
  if (isNaN(parsed)) {
    throw new Error(
      `Environment variable ${String(key)} is not a valid number: ${value}`
    )
  }
  return parsed
}

/**
 * Get an environment variable as a boolean
 * @param key - The environment variable key (typed)
 * @param defaultValue - Default value if the environment variable is not set
 * @returns The environment variable value as a boolean
 */
export const envBoolean = <K extends keyof EnvironmentVariables>(
  key: K,
  defaultValue: boolean = false
): boolean => {
  const value = process.env[key]
  if (!value) return defaultValue

  return value.toLowerCase() === 'true' || value === '1'
}
